def get_random_comment_time(
        parent_comment_timestamp, time_range_start=0.1, time_range_end=2, is_fake=False
):
    """
    获取在父评论时间戳之后的一定时间范围内的随机评论时间戳。

    Args:
        is_fake: 是否是fake   如果是 fake 就是父评论之后的0.1~2 小时    如果不是fake的则需要在当前时间近三天之内
        parent_comment_timestamp (Union[str, int, float]): 父评论的时间戳，可以是 ISO 格式的字符串，
                                                            或者是 Unix 时间戳（整数或浮点数）。
        time_range_start (float, optional): 随机时间差的起始小时数，默认为 0.2 小时。
        time_range_end (float, optional): 随机时间差的结束小时数，默认为 1 小时。
        随机时间差太长的会导致大概率超过当前时间,会导致大范围用的当前时间戳

    Returns:
        str: 随机生成的评论时间戳，以字符串形式返回。

    Raises:
        ValueError: 如果输入的 ISO 格式字符串无效。
        TypeError: 如果输入的 parent_comment_timestamp 不是 ISO 格式的字符串或 Unix 时间戳。

    细节:
    1. 在 parent_comment 的时间(时间戳形式)之后 time_range_start 小时到 time_range_end 小时之间随机取
    2. 避免 0 点到 6 点，如果随机时间落在 0~6 点区间，则从 6 点开始顺延随机的时间差
    3. 不超过当前时间
    """

    # 如果 parent_comment_timestamp 是字符串（ISO 格式），解析为 datetime 对象
    if isinstance(parent_comment_timestamp, str):
        try:
            # 将 ISO 格式字符串解析为 datetime 对象
            parent_comment_datetime = datetime.datetime.fromisoformat(
                parent_comment_timestamp
            )
        except ValueError:
            raise ValueError(
                "Invalid ISO8601 date string format: {}".format(
                    parent_comment_timestamp
                )
            )
    elif isinstance(parent_comment_timestamp, (int, float)):
        # 如果是数字型时间戳，直接转换为 datetime 对象
        parent_comment_datetime = datetime.datetime.fromtimestamp(
            parent_comment_timestamp
        )
    else:
        raise TypeError(
            "parent_comment_timestamp must be a string in ISO8601 format or an integer timestamp"
        )
    # 获取当前时间戳
    current_timestamp = int(time.time())
    # 将父评论时间转换为时间戳
    parent_comment_timestamp = int(parent_comment_datetime.timestamp())
    # 生成在父评论时间戳之后的 time_range_start 小时到 time_range_end 小时的随机时间差（秒）
    random_seconds = random.randint(
        time_range_start * 60 * 60, time_range_end * 60 * 60
    )
    random_comment_timestamp = parent_comment_timestamp + random_seconds
    # 转换为 datetime 便于处理时间逻辑
    random_comment_datetime = datetime.datetime.fromtimestamp(random_comment_timestamp)
    # 如果随机时间落在凌晨 0 点到 6 点之间，顺延相应的时间
    if 0 <= random_comment_datetime.hour < 6:
        # 计算偏移量（从凌晨 0 点到随机时间的时分秒差）
        offset = datetime.timedelta(
            hours=random_comment_datetime.hour,
            minutes=random_comment_datetime.minute,
            seconds=random_comment_datetime.second,
        )
        # 顺延时间：从早上 6 点开始加上偏移量
        adjusted_datetime = (
                datetime.datetime.combine(
                    random_comment_datetime.date(), datetime.time(6, 0, 0)
                )
                + offset
        )
    else:
        adjusted_datetime = random_comment_datetime

    # 如果是真人则需要判断是否在当前日期三天之内, 防止push的时候间隔太久
    if not is_fake:
        # 计算当前时间减去三天的时间戳
        current_datetime = datetime.datetime.fromtimestamp(current_timestamp)
        three_days_ago_timestamp = current_timestamp - (3 * 24 * 60 * 60)

        # 如果调整后的时间早于三天前，则只修改日期部分
        if adjusted_datetime.timestamp() < three_days_ago_timestamp:
            # 随机选择当前日期的前一天、前两天或前三天
            days_ago = random.randint(1, 3)
            random_date = current_datetime.date() - datetime.timedelta(days=days_ago)

            # 保留原时间的时分秒，只替换日期部分
            adjusted_datetime = datetime.datetime.combine(
                random_date,
                datetime.time(
                    hour=adjusted_datetime.hour,
                    minute=adjusted_datetime.minute,
                    second=adjusted_datetime.second,
                ),
            )
    # 确保调整后的时间不超过当前时间
    if adjusted_datetime.timestamp() > current_timestamp:
        adjusted_datetime = datetime.datetime.fromtimestamp(current_timestamp)
    # 返回最终时间戳
    return str(int(adjusted_datetime.timestamp()))